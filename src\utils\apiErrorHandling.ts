/**
 * API Error Handling Utilities
 * 
 * Provides consistent error handling for API responses across the application,
 * with special handling for unimplemented APIs (404s) and different error types.
 */

export interface ApiErrorInfo {
  isUnimplemented: boolean;
  isNetworkError: boolean;
  isServerError: boolean;
  isClientError: boolean;
  status?: number;
  message: string;
  originalError: any;
}

/**
 * Analyzes an API error and returns structured information about it
 */
export const analyzeApiError = (error: any): ApiErrorInfo => {
  const status = error?.response?.status || error?.status;
  const message = error?.response?.data?.message || error?.message || 'An unknown error occurred';

  return {
    isUnimplemented: status === 404,
    isNetworkError: !status || error?.code === 'NETWORK_ERROR' || error?.name === 'NetworkError',
    isServerError: status >= 500,
    isClientError: status >= 400 && status < 500,
    status,
    message,
    originalError: error
  };
};

/**
 * Determines if an API error should be silently handled (not shown to user)
 */
export const shouldSilentlyHandle = (errorInfo: ApiErrorInfo): boolean => {
  return errorInfo.isUnimplemented;
};

/**
 * Gets a user-friendly error message for display
 */
export const getUserFriendlyMessage = (errorInfo: ApiErrorInfo, apiName?: string): string => {
  if (errorInfo.isUnimplemented) {
    return `${apiName || 'This feature'} is coming soon`;
  }
  
  if (errorInfo.isNetworkError) {
    return 'Network error. Please check your connection and try again.';
  }
  
  if (errorInfo.isServerError) {
    return 'Server error. Please try again later.';
  }
  
  if (errorInfo.status === 401) {
    return 'You are not authorized to access this resource.';
  }
  
  if (errorInfo.status === 403) {
    return 'You do not have permission to access this resource.';
  }
  
  return errorInfo.message || 'An unexpected error occurred';
};

/**
 * Logs an error appropriately based on its type
 */
export const logApiError = (errorInfo: ApiErrorInfo, context: string): void => {
  if (errorInfo.isUnimplemented) {
    console.warn(`[${context}] API not yet implemented (404)`);
  } else {
    console.error(`[${context}] API error:`, {
      status: errorInfo.status,
      message: errorInfo.message,
      error: errorInfo.originalError
    });
  }
};

/**
 * Hook for handling API errors in a consistent way
 */
export const useApiErrorHandler = () => {
  const handleApiError = (
    error: any,
    options: {
      context: string;
      showNotification?: (message: string) => void;
      onUnimplemented?: () => void;
      onError?: (errorInfo: ApiErrorInfo) => void;
    }
  ): ApiErrorInfo => {
    const errorInfo = analyzeApiError(error);
    
    // Log the error
    logApiError(errorInfo, options.context);
    
    // Handle unimplemented APIs
    if (errorInfo.isUnimplemented) {
      options.onUnimplemented?.();
      return errorInfo;
    }
    
    // Show notification for real errors
    if (options.showNotification && !shouldSilentlyHandle(errorInfo)) {
      const message = getUserFriendlyMessage(errorInfo, options.context);
      options.showNotification(message);
    }
    
    // Call error handler
    options.onError?.(errorInfo);
    
    return errorInfo;
  };

  return { handleApiError };
};

/**
 * Wrapper for API calls that handles errors gracefully
 */
export const withApiErrorHandling = async <T>(
  apiCall: () => Promise<T>,
  options: {
    context: string;
    showNotification?: (message: string) => void;
    onUnimplemented?: () => T;
    onError?: (errorInfo: ApiErrorInfo) => T;
    fallbackValue?: T;
  }
): Promise<T> => {
  try {
    return await apiCall();
  } catch (error) {
    const errorInfo = analyzeApiError(error);
    logApiError(errorInfo, options.context);
    
    if (errorInfo.isUnimplemented && options.onUnimplemented) {
      return options.onUnimplemented();
    }
    
    if (options.showNotification && !shouldSilentlyHandle(errorInfo)) {
      const message = getUserFriendlyMessage(errorInfo, options.context);
      options.showNotification(message);
    }
    
    if (options.onError) {
      return options.onError(errorInfo);
    }
    
    if (options.fallbackValue !== undefined) {
      return options.fallbackValue;
    }
    
    throw error;
  }
};
